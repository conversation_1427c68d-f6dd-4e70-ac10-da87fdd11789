/**
 * 系统管理员认证服务
 * 专门处理系统管理员（超管、管理、员工）的登录认证和用户信息管理
 */

// 导入API模块
import authApi from '../api/auth.js'
import CryptoJS from 'crypto-js'

// 用户类型映射 - 映射数字类型到字符串
// 根据数据库表结构：1:超级管理员 2:管理员 3:员工
export const ADMIN_USER_TYPES = {
  1: 'admin',     // 超管
  2: 'manager',   // 管理
  3: 'employee'   // 员工
}

// 反向映射
export const ADMIN_USER_TYPE_NAMES = {
  1: '超管',
  2: '管理',
  3: '员工'
}

// 存储键名 - 与微信用户完全分离
const ADMIN_STORAGE_KEYS = {
  LOGIN_INFO: 'adminLoginInfo',
  USER_PERMISSIONS: 'adminUserPermissions'
}

/**
 * 系统管理员认证服务类
 */
class AdminAuthService {
  /**
   * 系统管理员登录认证
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise<Object>} 登录结果
   */
  async authenticate (username, password) {
    try {
      // 对密码进行MD5加密
      const hashedPassword = CryptoJS.MD5(password).toString()

      const response = await authApi.login({ username, password: hashedPassword })

      if (response && response.success && response.data) {
        const userInfo = response.data.userInfo
        const permissions = response.data.permissions || []

        // 映射用户类型
        const userType = ADMIN_USER_TYPES[userInfo.UserType] || 'employee'

        const loginInfo = {
          username: userInfo.Username || username,
          userId: userInfo.UserId || userInfo.Username || username,
          nickName: userInfo.NickName,
          userType: userType,
          userTypeCode: userInfo.UserType,
          accessToken: response.data.accessToken,
          permissions: permissions,
          loginTime: new Date().getTime()
        }

        // 保存登录信息
        this.saveLoginInfo(loginInfo)

        return {
          success: true,
          message: '登录成功',
          user: loginInfo
        }
      } else {
        const errorMessage = (response && response.msg) || '登录失败，请检查用户名和密码'
        return {
          success: false,
          message: errorMessage
        }
      }

    } catch (error) {
      console.error('Admin authentication error:', error)
      const errorMessage = error.message || '登录失败，请重试'
      return {
        success: false,
        message: errorMessage
      }
    }
  }

  /**
   * 保存管理员登录信息到存储
   * @param {Object} loginInfo
   */
  saveLoginInfo (loginInfo) {
    try {
      uni.setStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO, loginInfo)
      uni.setStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS, loginInfo.permissions)
    } catch (error) {
      console.error('Error saving admin login info:', error)
    }
  }

  /**
   * 获取当前管理员登录信息
   * @returns {Object|null}
   */
  getLoginInfo () {
    try {
      return uni.getStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO) || null
    } catch (error) {
      console.error('Error getting admin login info:', error)
      return null
    }
  }

  /**
   * 检查管理员是否已登录
   * @returns {boolean}
   */
  isLoggedIn () {
    const loginInfo = this.getLoginInfo()
    return !!(loginInfo && loginInfo.username && loginInfo.userId)
  }

  /**
   * 检查管理员是否有特定权限
   * @param {string} permission 
   * @returns {boolean}
   */
  hasPermission (permission) {
    try {
      const permissions = uni.getStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS) || []
      return permissions.includes('*') || permissions.includes(permission)
    } catch (error) {
      console.error('Error checking admin permission:', error)
      return false
    }
  }

  /**
   * 获取当前管理员用户类型
   * @returns {string|null}
   */
  getUserType () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.userType : null
  }

  /**
   * 获取当前管理员用户名
   * @returns {string|null}
   */
  getUsername () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.username : null
  }

  /**
   * 获取当前管理员用户ID
   * @returns {string|null}
   */
  getUserId () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.userId : null
  }

  /**
   * 获取当前管理员显示名称
   * @returns {string|null}
   */
  getUserDisplayName () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? (loginInfo.nickName || loginInfo.username) : null
  }

  /**
   * 管理员登出
   */
  async logout () {
    try {
      // 调用服务端登出API
      try {
        await authApi.logout()
      } catch (apiError) {
        console.warn('Server logout failed:', apiError)
      }

      // 清理本地存储
      uni.removeStorageSync(ADMIN_STORAGE_KEYS.LOGIN_INFO)
      uni.removeStorageSync(ADMIN_STORAGE_KEYS.USER_PERMISSIONS)

      return true
    } catch (error) {
      console.error('Error during admin logout:', error)
      return false
    }
  }

  /**
   * 检查登录会话是否有效（未过期）
   * @returns {boolean}
   */
  isSessionValid () {
    const loginInfo = this.getLoginInfo()
    if (!loginInfo || !loginInfo.loginTime) {
      return false
    }

    // 会话7天后过期
    const sessionDuration = 7 * 24 * 60 * 60 * 1000 // 7天（毫秒）
    const currentTime = new Date().getTime()

    return (currentTime - loginInfo.loginTime) < sessionDuration
  }

  /**
   * 刷新登录会话
   */
  refreshSession () {
    const loginInfo = this.getLoginInfo()
    if (loginInfo) {
      loginInfo.loginTime = new Date().getTime()
      this.saveLoginInfo(loginInfo)
    }
  }

  /**
   * 跳转到管理员登录页面
   */
  redirectToLogin () {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }

  /**
   * 根据管理员类型跳转到主页面
   */
  redirectToMain () {
    const userType = this.getUserType()

    console.log(`管理员登录成功! 用户类型: ${userType}`)

    // 跳转到管理仪表板（使用 switchTab 跳转到 TabBar 页面）
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      })
    }, 1000)
  }

  /**
   * 获取管理员用户类型显示文本
   * @returns {string}
   */
  getUserTypeText () {
    const loginInfo = this.getLoginInfo()
    if (!loginInfo) return '未登录'

    return ADMIN_USER_TYPE_NAMES[loginInfo.userTypeCode] || '未知'
  }
}

// 创建单例实例
const adminAuthService = new AdminAuthService()

// 导出服务实例和工具函数
export default adminAuthService
export { AdminAuthService, ADMIN_STORAGE_KEYS }

// 导出常用函数以便使用
export const getAdminLoginInfo = () => adminAuthService.getLoginInfo()
export const isAdminLoggedIn = () => adminAuthService.isLoggedIn()
export const getAdminUserType = () => adminAuthService.getUserType()
export const getAdminUsername = () => adminAuthService.getUsername()
export const getAdminUserId = () => adminAuthService.getUserId()
